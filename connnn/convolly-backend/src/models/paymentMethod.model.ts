import { Schema, Document, model } from 'mongoose';

export interface IPaymentMethod extends Document {
  userId: Schema.Types.ObjectId;
  stripePaymentMethodId: string;
  type: 'card';
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
    funding: string;
  };
  billingDetails: {
    name?: string;
    email?: string;
    address?: {
      city?: string;
      country?: string;
      line1?: string;
      line2?: string;
      postalCode?: string;
      state?: string;
    };
  };
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const PaymentMethodSchema = new Schema<IPaymentMethod>({
  userId: {
    type: Schema.Types.ObjectId,
    required: true,
    refPath: 'userModel'
  },
  stripePaymentMethodId: {
    type: String,
    required: true,
    unique: true
  },
  type: {
    type: String,
    enum: ['card'],
    default: 'card'
  },
  card: {
    brand: { type: String, required: true },
    last4: { type: String, required: true },
    expMonth: { type: Number, required: true },
    expYear: { type: Number, required: true },
    funding: { type: String, required: true }
  },
  billingDetails: {
    name: String,
    email: String,
    address: {
      city: String,
      country: String,
      line1: String,
      line2: String,
      postalCode: String,
      state: String
    }
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
PaymentMethodSchema.index({ userId: 1, isActive: 1 });
PaymentMethodSchema.index({ stripePaymentMethodId: 1 });

// Ensure only one default payment method per user
PaymentMethodSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    await PaymentMethod.updateMany(
      { userId: this.userId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

const PaymentMethod = model<IPaymentMethod>('PaymentMethod', PaymentMethodSchema);
export default PaymentMethod;
