import { Router } from 'express';
import { PaymentMethodController } from '../controllers/paymentMethodController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const paymentMethodRouter = Router();

// Add a new payment method
paymentMethodRouter.post(
  '/',
  withRequestBody(),
  isAuthenticated({ strictVerification: true }),
  PaymentMethodController.addPaymentMethod
);

// Get user's payment methods
paymentMethodRouter.get(
  '/',
  isAuthenticated({ strictVerification: true }),
  PaymentMethodController.getPaymentMethods
);

// Set default payment method
paymentMethodRouter.patch(
  '/:paymentMethodId/default',
  isAuthenticated({ strictVerification: true }),
  PaymentMethodController.setDefaultPaymentMethod
);

// Delete payment method
paymentMethodRouter.delete(
  '/:paymentMethodId',
  isAuthenticated({ strictVerification: true }),
  PaymentMethodController.deletePaymentMethod
);

export default paymentMethodRouter;
