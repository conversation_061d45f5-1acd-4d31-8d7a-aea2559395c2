import { Router } from 'express';
import { NotificationSettingsController } from '../controllers/notificationSettingsController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const notificationSettingsRouter = Router();

// Get user's notification settings
notificationSettingsRouter.get(
  '/',
  isAuthenticated({ strictVerification: true }),
  NotificationSettingsController.getNotificationSettings
);

// Update user's notification settings
notificationSettingsRouter.put(
  '/',
  withRequestBody(),
  isAuthenticated({ strictVerification: true }),
  NotificationSettingsController.updateNotificationSettings
);

// Update lesson reminder timing
notificationSettingsRouter.patch(
  '/reminder-timing',
  withRequestBody(),
  isAuthenticated({ strictVerification: true }),
  NotificationSettingsController.updateLessonReminderTiming
);

export default notificationSettingsRouter;
