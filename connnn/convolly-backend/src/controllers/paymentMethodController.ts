import { Request, Response } from 'express';
import Stripe from 'stripe';
import PaymentMethod from '../models/paymentMethod.model';
import Student from '../models/student';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export class PaymentMethodController {
  // Add a new payment method
  static async addPaymentMethod(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { cardNumber, expiryDate, cvv, cardholderName } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      // Get user details
      const user = await Student.findById(userId);
      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Ensure user has a Stripe customer ID
      let stripeCustomerId = user.stripeCustomerId;
      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstname} ${user.lastname}`,
        });
        stripeCustomerId = customer.id;
        user.stripeCustomerId = stripeCustomerId;
        await user.save();
      }

      // Parse expiry date (MM/YY format)
      const [expMonth, expYear] = expiryDate.split('/');
      const fullYear = parseInt(`20${expYear}`);

      // Create payment method in Stripe
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: {
          number: cardNumber,
          exp_month: parseInt(expMonth),
          exp_year: fullYear,
          cvc: cvv,
        },
        billing_details: {
          name: cardholderName,
          email: user.email,
        },
      });

      // Attach payment method to customer
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: stripeCustomerId,
      });

      // Check if this is the user's first payment method
      const existingPaymentMethods = await PaymentMethod.find({ 
        userId, 
        isActive: true 
      });
      const isFirstPaymentMethod = existingPaymentMethods.length === 0;

      // Save payment method to database
      const savedPaymentMethod = new PaymentMethod({
        userId,
        stripePaymentMethodId: paymentMethod.id,
        type: 'card',
        card: {
          brand: paymentMethod.card?.brand || '',
          last4: paymentMethod.card?.last4 || '',
          expMonth: paymentMethod.card?.exp_month || 0,
          expYear: paymentMethod.card?.exp_year || 0,
          funding: paymentMethod.card?.funding || '',
        },
        billingDetails: {
          name: cardholderName,
          email: user.email,
        },
        isDefault: isFirstPaymentMethod, // First payment method becomes default
        isActive: true,
      });

      await savedPaymentMethod.save();

      createOkResponse(res, {
        success: true,
        message: 'Payment method added successfully',
        data: {
          id: savedPaymentMethod._id,
          card: savedPaymentMethod.card,
          isDefault: savedPaymentMethod.isDefault,
        },
      });
    } catch (error: any) {
      console.error('Error adding payment method:', error);
      createErrorResponse(res, error.message || 'Failed to add payment method', 500);
    }
  }

  // Get user's payment methods
  static async getPaymentMethods(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      const paymentMethods = await PaymentMethod.find({
        userId,
        isActive: true,
      }).sort({ isDefault: -1, createdAt: -1 });

      createOkResponse(res, {
        success: true,
        data: paymentMethods.map(pm => ({
          id: pm._id,
          card: pm.card,
          isDefault: pm.isDefault,
          createdAt: pm.createdAt,
        })),
      });
    } catch (error: any) {
      console.error('Error fetching payment methods:', error);
      createErrorResponse(res, 'Failed to fetch payment methods', 500);
    }
  }

  // Set default payment method
  static async setDefaultPaymentMethod(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { paymentMethodId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      // Find the payment method
      const paymentMethod = await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId,
        isActive: true,
      });

      if (!paymentMethod) {
        createErrorResponse(res, 'Payment method not found', 404);
        return;
      }

      // Update default status
      await PaymentMethod.updateMany(
        { userId, isActive: true },
        { isDefault: false }
      );

      paymentMethod.isDefault = true;
      await paymentMethod.save();

      createOkResponse(res, {
        success: true,
        message: 'Default payment method updated successfully',
      });
    } catch (error: any) {
      console.error('Error setting default payment method:', error);
      createErrorResponse(res, 'Failed to update default payment method', 500);
    }
  }

  // Delete payment method
  static async deletePaymentMethod(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { paymentMethodId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      // Find the payment method
      const paymentMethod = await PaymentMethod.findOne({
        _id: paymentMethodId,
        userId,
        isActive: true,
      });

      if (!paymentMethod) {
        createErrorResponse(res, 'Payment method not found', 404);
        return;
      }

      // Detach from Stripe
      await stripe.paymentMethods.detach(paymentMethod.stripePaymentMethodId);

      // Soft delete from database
      paymentMethod.isActive = false;
      await paymentMethod.save();

      // If this was the default payment method, set another one as default
      if (paymentMethod.isDefault) {
        const nextPaymentMethod = await PaymentMethod.findOne({
          userId,
          isActive: true,
          _id: { $ne: paymentMethodId },
        });

        if (nextPaymentMethod) {
          nextPaymentMethod.isDefault = true;
          await nextPaymentMethod.save();
        }
      }

      createOkResponse(res, {
        success: true,
        message: 'Payment method deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting payment method:', error);
      createErrorResponse(res, 'Failed to delete payment method', 500);
    }
  }
}
