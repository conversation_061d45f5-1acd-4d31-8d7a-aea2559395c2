import { Request, Response } from 'express';
import Student from '../models/student';
import <PERSON><PERSON> from '../models/tutor';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { createOkResponse } from '../utils/misc';

export class NotificationSettingsController {
  // Get user's notification settings
  static async getNotificationSettings(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId).select('settingsPreference');
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId).select('settingsPreference');
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Ensure default values if settingsPreference doesn't exist
      const settings = user.settingsPreference || {
        withLessonUpdates: true,
        withLessonReminders: false,
        withMessageUpdates: true,
        lessonReminderMS: 300000,
      };

      createOkResponse(res, {
        success: true,
        data: {
          lessonUpdates: settings.withLessonUpdates,
          lessonReminders: settings.withLessonReminders,
          messageUpdates: settings.withMessageUpdates,
          lessonReminderMS: settings.lessonReminderMS,
        },
      });
    } catch (error: any) {
      console.error('Error fetching notification settings:', error);
      createErrorResponse(res, 'Failed to fetch notification settings', 500);
    }
  }

  // Update user's notification settings
  static async updateNotificationSettings(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const { lessonUpdates, lessonReminders, messageUpdates } = req.body;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      // Validate input
      if (
        typeof lessonUpdates !== 'boolean' ||
        typeof lessonReminders !== 'boolean' ||
        typeof messageUpdates !== 'boolean'
      ) {
        createErrorResponse(res, 'Invalid notification settings format', 400);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Update notification settings
      if (!user.settingsPreference) {
        user.settingsPreference = {
          withLessonUpdates: true,
          withLessonReminders: false,
          withMessageUpdates: true,
          lessonReminderMS: 300000,
        };
      }

      user.settingsPreference.withLessonUpdates = lessonUpdates;
      user.settingsPreference.withLessonReminders = lessonReminders;
      user.settingsPreference.withMessageUpdates = messageUpdates;

      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Notification settings updated successfully',
        data: {
          lessonUpdates: user.settingsPreference.withLessonUpdates,
          lessonReminders: user.settingsPreference.withLessonReminders,
          messageUpdates: user.settingsPreference.withMessageUpdates,
        },
      });
    } catch (error: any) {
      console.error('Error updating notification settings:', error);
      createErrorResponse(res, 'Failed to update notification settings', 500);
    }
  }

  // Update lesson reminder timing (in milliseconds)
  static async updateLessonReminderTiming(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const { lessonReminderMS } = req.body;

      if (!userId) {
        createErrorResponse(res, 'User not authenticated', 401);
        return;
      }

      // Validate input
      if (typeof lessonReminderMS !== 'number' || lessonReminderMS < 0) {
        createErrorResponse(res, 'Invalid lesson reminder timing', 400);
        return;
      }

      let user;
      if (userRole === 'student') {
        user = await Student.findById(userId);
      } else if (userRole === 'tutor') {
        user = await Tutor.findById(userId);
      } else {
        createErrorResponse(res, 'Invalid user role', 400);
        return;
      }

      if (!user) {
        createErrorResponse(res, 'User not found', 404);
        return;
      }

      // Update lesson reminder timing
      if (!user.settingsPreference) {
        user.settingsPreference = {
          withLessonUpdates: true,
          withLessonReminders: false,
          withMessageUpdates: true,
          lessonReminderMS: 300000,
        };
      }

      user.settingsPreference.lessonReminderMS = lessonReminderMS;
      await user.save();

      createOkResponse(res, {
        success: true,
        message: 'Lesson reminder timing updated successfully',
        data: {
          lessonReminderMS: user.settingsPreference.lessonReminderMS,
        },
      });
    } catch (error: any) {
      console.error('Error updating lesson reminder timing:', error);
      createErrorResponse(res, 'Failed to update lesson reminder timing', 500);
    }
  }
}
