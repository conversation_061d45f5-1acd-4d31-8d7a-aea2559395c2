// TutorSidebar.jsx
import React, { useEffect, useRef } from "react";
import SidebarLinks from "./SidebarLinks";
import { useSelector } from "react-redux";
import img from "@/assets/svgs/userVector.svg";

const TutorSidebar = ({ isOpen, toggleSidebar, setShowLogoutModal }) => {
	const sidebarRef = useRef(null);
	const user = useSelector((state) => state?.app?.userInfo?.user);

	useEffect(() => {
		const handleClickOutside = (event) => {
			// Check if the click is on the navbar toggle button
			const navbarToggle = document.querySelector(
				".lg\\:hidden.p-1.rounded-md"
			); // Match your navbar toggle button class

			if (
				sidebarRef.current &&
				!sidebarRef.current.contains(event.target) &&
				isOpen &&
				event.target !== navbarToggle &&
				!navbarToggle?.contains(event.target)
			) {
				toggleSidebar();
			}
		};

		document.addEventListener("mousedown", handleClickOutside);

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen, toggleSidebar]);

	return (
		<div
			ref={sidebarRef}
			className={`fixed lg:relative z-50 lg:z-auto lg:block w-[256px] p-3 overflow-auto h-full bg-white transition-all duration-300 ${
				isOpen ? "left-0" : "-left-full"
			} lg:left-0`}
		>
			<div className="my-5">
				<div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
					<div className="flex items-center p-4 gap-3">
						<div className="relative">
							<img
								src={user?.image || img}
								alt={user?.fullname}
								className="w-10 h-10 rounded-full object-cover border-2 border-white shadow-md"
							/>
						</div>

						<div>
							<h3 className="font-semibold text-gray-900">{user?.fullname}</h3>
							<p className="text-sm text-gray-600 flex items-center gap-1">
								{user.role}
							</p>
						</div>
					</div>
				</div>
			</div>

			<div>
				<SidebarLinks
					toggleSidebar={toggleSidebar}
					setShowLogoutModal={setShowLogoutModal}
				/>
			</div>
		</div>
	);
};

export default TutorSidebar;
